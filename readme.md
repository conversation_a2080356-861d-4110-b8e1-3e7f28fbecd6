Instruções gerais para gerar landing page completa “Imposto de Renda”

Crie um projeto estático composto por index.html, styles.css e script.js (pasta assets/ para imagens caso julgue necessário).

O código deve ser mobile‑first, responsivo até ≥1440 px, acessível (tags semânticas, ARIA onde couber) e leve (nota Lighthouse ≥ 90).

Use HTML5 puro, CSS vanilla (sem frameworks) e JS ES6 minimalista. 

Estruture comentários claros para futura manutenção.

Todas as copies abaixo podem ser mantidas ou substituídas depois; marque‑as com <!-- TODO: editar copy -->.

Siga a paleta, tipografia e interação descritas e implemente todos os componentes.

1. Paleta & tipografia
txt
Copiar
Editar
Verde‑principal: #25D366          (botões, ícones WhatsApp)
Cinza‑texto:     #333333
Cinza‑fundo:     #F8F9FA
Fonte primária:  "<PERSON>pin<PERSON>", sans‑serif – importar via Google Fonts
2. Estrutura de seções (na ordem)
ID	Bloco	Requisitos de layout & conteúdo	Interação
hero	Headline H1 (32‑36 px) + sub‑headline H4	Espaçamento generoso; fundo branco	Dois botões gêmeos “Fale comigo pelo WhatsApp” → https://wa.me/5599999999999
proof	Mini‑banner texto “+ de 200 declarações entregues em 2024”	Tipografia 18 px, icone‑dólar ilustrativo	Estático
pains	Lista UL de 3 problemas (multas, deduções, atendimento)	Ícone de alerta antes de cada LI	—
benefits	Grid 1 × 5 com ícone redondo + título curto (ex.: “Economia de tempo”)	Cards 120 × 120 px	—
cta‑1	Botão central verde (mesmo link WhatsApp)	Margem‑top 48 px	—
testimonials	Carrossel de 12 depoimentos (foto circular 64 px + texto)	Implementar com SwiperJS ou JS nativo; setas prev/next; paginação	Autoplay 6 s
steps	3 passos numerados (1️⃣ Contato, 2️⃣ Envio docs, 3️⃣ Relaxar)	Layout flex row no desktop, column no mobile	—
slogan	H3 “Simples, seguro e eficiente!” centralizado	Letter‑spacing 0.5 px	—
faq	Accordion de 9 Q&As	Esconder/mostrar com JS; setas giram 180°	Primeiro item aberto = false
form	HTML <form> com: Nome*, E‑mail*, Celular*, Cidade (select), Assunto (select), Mensagem (textarea)	required, máscara tel. BR +55; onSubmit exibe toast “Mensagem enviada”	Enviar para mailto:<EMAIL> ou só console.log
footer	Contém: e‑mail, telefone (DDD 11), links sociais (Instagram, YouTube, LinkedIn), copyright, crédito “Desenvolvido por Barros Design”	Links ‑ target="_blank"	—
whatsapp‑float	Botão flutuante 48 px (bottom‑right)	Abre mesma URL wa.me	Sticky via CSS
scroll‑top	Ícone seta up 40 px surge após 200 px scroll	Smooth scroll JS	—

3. Detalhes de UX / interação
Hover states: botões escurecem 10%.

Focus outline: 2 px verde em inputs/botões para acessibilidade.

Lazy‑load: loading="lazy" nas imagens do carrossel.

SEO: preencha title, meta description, alt nas imagens, use h1 → h4 hierárquicos.

Drawers: accordion icon usa transição 150 ms ease‑in‑out.

Scroll‑to‑top: fade‑in 0.3 s quando aparece.

Responsividade:

Mobile até 767 px: nav colunar, paddings 16 px.

Tablet 768‑1023 px: grid 2 colunas nos benefícios.

Desktop ≥1024 px: grid 5 colunas, carrossel 3 cards visíveis.

4. Estrutura de arquivos
bash
Copiar
Editar
/index.html
/assets/
  /img/ (placeholders .jpg/.svg)
  /css/styles.css
  /js/script.js
5. Exemplos de copy (placeholder)
html
Copiar
Editar
<h1>Declaração de Imposto de Renda rápida e sem dor de cabeça</h1>
<h4>Evite multas e garanta todas as deduções em minutos!</h4>
<!-- TODO: editar copy -->
6. JS funcionalidades a implementar em script.js
js
Copiar
Editar
// 1. Swiper (ou carrossel simples) initialisation
// 2. FAQ accordion toggle
// 3. Show/hide scroll‑to‑top button
// 4. Form validation & toast (pode usar alert)
// 5. Smooth Scroll for anchors
7. Checklist final
 Lighthouse mobile ≥ 90.

 W3C validator sem erros críticos.

 Google Fonts pré‑connect.

 Favicon 32 × 32.

 robots.txt e sitemap.xml opcionais comentados.