// ===== MODERN JAVASCRIPT FOR CG ASSESSORIA CONTÁBIL =====

class CGWebsite {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initScrollEffects();
        this.initMobileMenu();
        this.initCounterAnimations();
        this.initSmoothScrolling();
        this.initFormHandling();
        this.initAOS();
    }

    // ===== EVENT LISTENERS =====
    setupEventListeners() {
        window.addEventListener('scroll', this.handleScroll.bind(this));
        window.addEventListener('load', this.handlePageLoad.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    // ===== SCROLL EFFECTS =====
    handleScroll() {
        this.updateHeaderOnScroll();
        this.updateScrollIndicator();
    }

    updateHeaderOnScroll() {
        const header = document.getElementById('header');
        if (!header) return;

        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }

    updateScrollIndicator() {
        const scrollIndicator = document.querySelector('.scroll-indicator');
        if (!scrollIndicator) return;

        if (window.scrollY > 200) {
            scrollIndicator.style.opacity = '0';
        } else {
            scrollIndicator.style.opacity = '1';
        }
    }

    // ===== MOBILE MENU =====
    initMobileMenu() {
        const navToggle = document.getElementById('nav-toggle');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        const mobileMenuClose = document.getElementById('mobile-menu-close');
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

        if (!navToggle || !mobileMenuOverlay) return;

        navToggle.addEventListener('click', () => {
            navToggle.classList.toggle('active');
            mobileMenuOverlay.classList.toggle('active');
            document.body.style.overflow = mobileMenuOverlay.classList.contains('active') ? 'hidden' : '';
        });

        if (mobileMenuClose) {
            mobileMenuClose.addEventListener('click', () => {
                this.closeMobileMenu();
            });
        }

        mobileNavLinks.forEach(link => {
            link.addEventListener('click', () => {
                this.closeMobileMenu();
            });
        });

        // Close on overlay click
        mobileMenuOverlay.addEventListener('click', (e) => {
            if (e.target === mobileMenuOverlay) {
                this.closeMobileMenu();
            }
        });
    }

    closeMobileMenu() {
        const navToggle = document.getElementById('nav-toggle');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        
        if (navToggle) navToggle.classList.remove('active');
        if (mobileMenuOverlay) mobileMenuOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    // ===== COUNTER ANIMATIONS =====
    initCounterAnimations() {
        const counters = document.querySelectorAll('[data-count]');
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        counters.forEach(counter => observer.observe(counter));
    }

    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-count'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            const suffix = element.textContent.includes('%') ? '%' : 
                          element.textContent.includes('+') ? '+' : 
                          element.textContent.includes('h') ? 'h' : '';
            
            element.textContent = Math.floor(current) + suffix;
        }, 16);
    }

    // ===== SMOOTH SCROLLING =====
    initSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');
        
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
                    const targetPosition = targetElement.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // ===== FORM HANDLING =====
    initFormHandling() {
        this.initMultiStepForm();
    }

    initMultiStepForm() {
        const form = document.getElementById('multi-step-form');
        if (!form) return;

        const steps = form.querySelectorAll('.form-step');
        const nextButtons = form.querySelectorAll('.next-step');
        const prevButtons = form.querySelectorAll('.prev-step');
        const progressSteps = form.querySelectorAll('.progress-step');
        let currentStep = 0;

        nextButtons.forEach(button => {
            button.addEventListener('click', () => {
                if (this.validateStep(currentStep)) {
                    this.nextStep();
                }
            });
        });

        prevButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.prevStep();
            });
        });

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitForm();
        });

        this.nextStep = () => {
            if (currentStep < steps.length - 1) {
                steps[currentStep].classList.remove('active');
                progressSteps[currentStep].classList.remove('active');
                currentStep++;
                steps[currentStep].classList.add('active');
                progressSteps[currentStep].classList.add('active');
            }
        };

        this.prevStep = () => {
            if (currentStep > 0) {
                steps[currentStep].classList.remove('active');
                progressSteps[currentStep].classList.remove('active');
                currentStep--;
                steps[currentStep].classList.add('active');
                progressSteps[currentStep].classList.add('active');
            }
        };
    }

    validateStep(step) {
        const currentStepElement = document.querySelector('.form-step.active');
        const requiredFields = currentStepElement.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.style.borderColor = '#ef4444';
                isValid = false;
            } else {
                field.style.borderColor = '#e2e8f0';
            }
        });

        return isValid;
    }

    submitForm() {
        const formData = new FormData(document.getElementById('multi-step-form'));
        const data = Object.fromEntries(formData);
        
        // Create WhatsApp message
        const message = this.createWhatsAppMessage(data);
        const whatsappUrl = `https://wa.me/5521994069818?text=${encodeURIComponent(message)}`;
        
        window.open(whatsappUrl, '_blank');
    }

    createWhatsAppMessage(data) {
        return `Olá! Gostaria de solicitar os serviços da CG Assessoria Contábil.

*Dados Pessoais:*
Nome: ${data.nome || 'Não informado'}
E-mail: ${data.email || 'Não informado'}
WhatsApp: ${data.whatsapp || 'Não informado'}
Cidade: ${data.cidade || 'Não informado'}

*Serviço Solicitado:*
${data.servico || 'Não informado'}

*Detalhes:*
${data.detalhes || 'Não informado'}

Aguardo retorno. Obrigado!`;
    }

    // ===== AOS INITIALIZATION =====
    initAOS() {
        // Simple AOS-like functionality
        const elements = document.querySelectorAll('[data-aos]');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, { threshold: 0.1 });

        elements.forEach(el => observer.observe(el));
    }

    // ===== PAGE LOAD =====
    handlePageLoad() {
        document.body.classList.add('loaded');
        this.initCounterAnimations();
    }

    // ===== RESIZE =====
    handleResize() {
        // Handle responsive adjustments
        if (window.innerWidth >= 1024) {
            this.closeMobileMenu();
        }
    }
}

// ===== INITIALIZE =====
document.addEventListener('DOMContentLoaded', () => {
    new CGWebsite();
});

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
