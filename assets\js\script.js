/**
 * CG Assessoria - Declaração de Imposto de Renda
 * Main JavaScript file
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initFaqAccordion();
    initSmoothScroll();
    initFormValidation();
    initTestimonialsCarousel();
    initMobileMenu();
    initHeaderScroll();
    initFadeInSections();
    initScrollDownArrow();
});

/**
 * Initialize Scroll Down Arrow functionality
 */
function initScrollDownArrow() {
    const scrollDownArrow = document.querySelector('.scroll-down-arrow');

    if (!scrollDownArrow) return;

    // Make sure the arrow is visible when the page loads
    scrollDownArrow.style.opacity = '1';
    scrollDownArrow.style.visibility = 'visible';

    // Determinar se é um dispositivo móvel
    const isMobile = window.innerWidth <= 768;

    // Add a small animation to draw attention to the arrow when page loads
    setTimeout(() => {
        scrollDownArrow.classList.add('attention');
        setTimeout(() => {
            scrollDownArrow.classList.remove('attention');
        }, 1500);
    }, 1000);

    // Hide arrow when scrolling down - diferentes limiares para desktop e mobile
    window.addEventListener('scroll', function() {
        const scrollThreshold = isMobile ? 100 : 150; // Limiar menor para mobile

        if (window.scrollY > scrollThreshold) {
            scrollDownArrow.style.opacity = '0';
            scrollDownArrow.style.visibility = 'hidden';
        } else {
            scrollDownArrow.style.opacity = '1';
            scrollDownArrow.style.visibility = 'visible';
        }
    });

    // Atualizar o estado de mobile quando a janela for redimensionada
    window.addEventListener('resize', function() {
        const wasIsMobile = isMobile;
        const newIsMobile = window.innerWidth <= 768;

        // Se houve mudança no estado de mobile, recarregar a página para aplicar os estilos corretos
        if (wasIsMobile !== newIsMobile) {
            location.reload();
        }
    });

    // Smooth scroll when arrow is clicked
    scrollDownArrow.querySelector('a').addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
            window.scrollTo({
                top: targetElement.offsetTop - 20,
                behavior: 'smooth'
            });
        }
    });
}

/**
 * Initialize FAQ Accordion functionality
 */
function initFaqAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', () => {
            // Toggle active class on the clicked item
            item.classList.toggle('active');

            // Close other items
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
        });
    });
}

// Scroll to Top functionality removed

/**
 * Initialize Smooth Scroll for anchor links
 */
function initSmoothScroll() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');

    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 20,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Initialize Form Validation and submission
 */
function initFormValidation() {
    const form = document.getElementById('contact-form');
    const phoneInput = document.getElementById('phone');

    // Simple phone mask
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');

            if (value.length > 0) {
                // Format as +55 (XX) XXXXX-XXXX
                if (value.length <= 2) {
                    value = `+${value}`;
                } else if (value.length <= 4) {
                    value = `+${value.substring(0, 2)} (${value.substring(2)}`;
                } else if (value.length <= 6) {
                    value = `+${value.substring(0, 2)} (${value.substring(2, 4)}) ${value.substring(4)}`;
                } else if (value.length <= 11) {
                    value = `+${value.substring(0, 2)} (${value.substring(2, 4)}) ${value.substring(4, 9)}-${value.substring(9)}`;
                } else {
                    value = `+${value.substring(0, 2)} (${value.substring(2, 4)}) ${value.substring(4, 9)}-${value.substring(9, 13)}`;
                }
            }

            e.target.value = value;
        });
    }

    // Form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Basic validation
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const city = document.getElementById('city').value || 'Não informado';
            const subject = document.getElementById('subject').value || 'Não informado';
            const message = document.getElementById('message').value || 'Não informado';

            if (!name || !email || !phone) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Por favor, insira um e-mail válido.');
                return;
            }

            // Mostrar mensagem de carregamento
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Redirecionando...';
            submitBtn.disabled = true;

            // Preparar a mensagem para o WhatsApp
            const whatsappMessage = `
*Nova mensagem do site CG Assessoria Contábil*

*Nome:* ${name}
*E-mail:* ${email}
*Telefone:* ${phone}
*Cidade:* ${city}
*Assunto:* ${subject}
*Mensagem:* ${message}

_Mensagem enviada através do formulário do site._
            `.trim();

            // Codificar a mensagem para URL
            const encodedMessage = encodeURIComponent(whatsappMessage);

            // Número do WhatsApp
            const whatsappNumber = '5521994069818';

            // URL do WhatsApp
            const whatsappURL = `https://wa.me/${whatsappNumber}?text=${encodedMessage}`;

            // Redirecionar para o WhatsApp após um pequeno delay para mostrar o loading
            setTimeout(() => {
                // Restaurar o botão
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;

                // Redirecionar para o WhatsApp
                window.open(whatsappURL, '_blank');

                // Resetar o formulário
                form.reset();
            }, 1000);
        });
    }
}

/**
 * Initialize Testimonials Carousel
 * This is a simple carousel implementation without external libraries
 */
function initTestimonialsCarousel() {
    const carousel = document.querySelector('.testimonials-carousel');

    if (!carousel) return;

    const container = carousel.querySelector('.swiper-container');
    const wrapper = carousel.querySelector('.swiper-wrapper');
    const slides = carousel.querySelectorAll('.swiper-slide');
    const pagination = carousel.querySelector('.swiper-pagination');
    const prevBtn = carousel.querySelector('.swiper-button-prev');
    const nextBtn = carousel.querySelector('.swiper-button-next');

    let currentIndex = 0;
    let slideWidth = 0;
    let slidesToShow = 1;
    let autoplayInterval;
    // Definindo o tempo para 3 segundos (3000ms)
    const autoplayDelay = 3000;

    // Calculate how many slides to show based on screen width
    function calculateSlidesToShow() {
        if (window.innerWidth >= 1200) {
            return 3; // Large Desktop: show 3 slides
        } else if (window.innerWidth >= 992) {
            return 2; // Desktop: show 2 slides
        } else if (window.innerWidth >= 768) {
            return 2; // Tablet: show 2 slides
        } else {
            return 1; // Mobile: show 1 slide
        }
    }

    // Update carousel dimensions and configuration
    function updateCarousel() {
        slidesToShow = calculateSlidesToShow();
        // Ensure container width is not exceeding viewport
        const containerWidth = Math.min(container.offsetWidth, window.innerWidth);
        slideWidth = containerWidth / slidesToShow;

        // Set slide widths
        slides.forEach(slide => {
            slide.style.width = `${slideWidth}px`;
            // Ensure content inside slides doesn't overflow
            slide.style.maxWidth = `${slideWidth}px`;
            slide.style.boxSizing = 'border-box';
            slide.style.overflow = 'hidden';
        });

        // Update wrapper width
        wrapper.style.width = `${slideWidth * slides.length}px`;
        wrapper.style.maxWidth = `${slideWidth * slides.length}px`;

        // Reset position
        goToSlide(currentIndex);

        // Create pagination bullets
        createPagination();
    }

    // Go to specific slide
    function goToSlide(index) {
        // Ensure index is within bounds
        if (index < 0) {
            index = slides.length - slidesToShow;
        } else if (index > slides.length - slidesToShow) {
            index = 0;
        }

        currentIndex = index;
        wrapper.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
        wrapper.style.transition = 'transform 0.3s ease-in-out';

        // Update pagination
        updatePagination();
    }

    // Create pagination bullets
    function createPagination() {
        if (!pagination) return;

        pagination.innerHTML = '';
        const totalPages = slides.length - slidesToShow + 1;

        for (let i = 0; i < totalPages; i++) {
            const bullet = document.createElement('span');
            bullet.classList.add('swiper-pagination-bullet');
            bullet.addEventListener('click', () => goToSlide(i));
            pagination.appendChild(bullet);
        }

        updatePagination();
    }

    // Update active pagination bullet
    function updatePagination() {
        if (!pagination) return;

        const bullets = pagination.querySelectorAll('.swiper-pagination-bullet');
        bullets.forEach((bullet, index) => {
            if (index === currentIndex) {
                bullet.classList.add('swiper-pagination-bullet-active');
            } else {
                bullet.classList.remove('swiper-pagination-bullet-active');
            }
        });
    }

    // Start autoplay
    function startAutoplay() {
        // Limpa qualquer intervalo existente
        stopAutoplay();
        // Configura um novo intervalo com o tempo de 3 segundos
        autoplayInterval = setInterval(() => {
            goToSlide(currentIndex + 1);
        }, autoplayDelay);
        console.log("Carrossel iniciado com intervalo de " + autoplayDelay + "ms");
    }

    // Stop autoplay
    function stopAutoplay() {
        if (autoplayInterval) {
            clearInterval(autoplayInterval);
        }
    }

    // Initialize carousel
    function initCarousel() {
        updateCarousel();

        // Add event listeners
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                goToSlide(currentIndex - 1);
                stopAutoplay();
                startAutoplay();
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                goToSlide(currentIndex + 1);
                stopAutoplay();
                startAutoplay();
            });
        }

        // Pause autoplay on hover
        container.addEventListener('mouseenter', stopAutoplay);
        container.addEventListener('mouseleave', startAutoplay);

        // Update on window resize
        window.addEventListener('resize', updateCarousel);

        // Força a parada de qualquer autoplay existente
        stopAutoplay();

        // Inicia o autoplay com o novo intervalo de 3 segundos
        setTimeout(() => {
            startAutoplay();
        }, 100);
    }

    // Initialize the carousel
    initCarousel();
}

/**
 * Initialize Mobile Menu functionality
 */
function initMobileMenu() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    if (!mobileMenuToggle || !navMenu) return;

    // Toggle menu when button is clicked
    mobileMenuToggle.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        mobileMenuToggle.classList.toggle('active');

        // Change icon based on menu state
        const icon = mobileMenuToggle.querySelector('i');
        if (icon) {
            if (navMenu.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        }
    });

    // Close menu when a link is clicked
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navMenu.classList.remove('active');
            mobileMenuToggle.classList.remove('active');

            const icon = mobileMenuToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!navMenu.contains(event.target) && !mobileMenuToggle.contains(event.target) && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            mobileMenuToggle.classList.remove('active');

            const icon = mobileMenuToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        }
    });
}

/**
 * Initialize Header Scroll functionality
 */
function initHeaderScroll() {
    const header = document.querySelector('.header');

    if (!header) return;

    // Add scrolled class to header when page is scrolled
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

/**
 * Initialize Fade-in Sections on scroll
 * Uses Intersection Observer API to detect when elements enter the viewport
 */
function initFadeInSections() {
    // Elements to animate - expanded to include all major sections and elements
    const sections = [
        // Main sections
        '.hero-logo',
        '.hero-content',
        '.proof-banner',
        '.pains h2',
        '.pains-list li',
        '.benefits h2',
        '.benefit-card',
        '.cta-content',
        '.testimonials h2',
        '.testimonial-card',
        '.steps h2',
        '.step',
        '.slogan-content',
        '.faq h2',
        '.faq-item',
        '.form h2',
        '.form form',

        // Additional elements
        '.footer-content',
        '.footer-info',
        '.whatsapp-float'
    ];

    // Add fade-in-section class to all elements
    sections.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
            el.classList.add('fade-in-section');

            // Add delay classes for staggered animations
            if (selector === '.pains-list li' ||
                selector === '.benefit-card' ||
                selector === '.testimonial-card' ||
                selector === '.step' ||
                selector === '.faq-item') {
                // Calculate delay class (1-5)
                const delayClass = `delay-${(index % 5) + 1}`;
                el.classList.add(delayClass);
            }
        });
    });

    // Create the observer
    const observerOptions = {
        root: null, // Use viewport as root
        rootMargin: '0px',
        threshold: 0.15 // Trigger when 15% of the element is visible
    };

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('is-visible');
                // Unobserve after animation is triggered
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all elements with fade-in-section class
    const fadeElements = document.querySelectorAll('.fade-in-section');
    fadeElements.forEach(element => {
        observer.observe(element);
    });
}
