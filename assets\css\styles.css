/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-50: #f0fdf4;
    --primary-100: #dcfce7;
    --primary-200: #bbf7d0;
    --primary-300: #86efac;
    --primary-400: #4ade80;
    --primary-500: #22c55e;
    --primary-600: #16a34a;
    --primary-700: #15803d;
    --primary-800: #166534;
    --primary-900: #14532d;

    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;

    --accent-blue: #3b82f6;
    --accent-purple: #8b5cf6;
    --accent-orange: #f59e0b;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-700) 0%, var(--secondary-800) 100%);
    --gradient-hero: linear-gradient(135deg, #1e3a8a 0%, #1e40af 25%, #3b82f6 50%, #60a5fa 75%, #93c5fd 100%);
    --gradient-text: linear-gradient(135deg, var(--primary-500) 0%, var(--accent-blue) 100%);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    --space-4xl: 6rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.6;
    color: var(--secondary-800);
    background-color: var(--secondary-50);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-xl);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-2xl);
    }
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: var(--secondary-900);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 600;
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 600;
}

p {
    margin-bottom: var(--space-md);
    line-height: 1.7;
    color: var(--secondary-600);
}

.text-large {
    font-size: 1.125rem;
    line-height: 1.6;
}

.text-small {
    font-size: 0.875rem;
    line-height: 1.5;
}

.gradient-text {
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== BUTTONS ===== */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-lg) var(--space-2xl);
    border: none;
    border-radius: var(--radius-lg);
    font-family: var(--font-secondary);
    font-weight: 600;
    font-size: 1.1rem;
    line-height: 1;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    overflow: hidden;
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.btn-hero {
    padding: var(--space-xl) var(--space-3xl);
    font-size: 1.25rem;
    border-radius: var(--radius-xl);
    font-weight: 700;
}

.btn-large {
    padding: var(--space-xl) var(--space-3xl);
    font-size: 1.25rem;
    font-weight: 700;
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover .btn-shine {
    left: 100%;
}

/* ===== SECTION COMPONENTS ===== */
.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    background: var(--primary-100);
    color: var(--primary-700);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--space-lg);
}

.section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto var(--space-4xl);
}

.section-title {
    margin-bottom: var(--space-lg);
}

.section-description {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--secondary-600);
    margin-bottom: 0;
}

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.header.scrolled .navbar {
    padding: var(--space-xl) 0;
}

.navbar {
    padding: var(--space-3xl) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .logo {
    height: 85px;
    width: auto;
    transition: all var(--transition-normal);
}

.header.scrolled .nav-brand .logo {
    height: 60px;
}

.nav-menu {
    display: none;
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--space-2xl);
    list-style: none;
}

.nav-link {
    position: relative;
    color: var(--secondary-700);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all var(--transition-normal);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover {
    color: var(--primary-600);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: none;
}

.nav-cta {
    padding: var(--space-lg) var(--space-2xl);
    font-size: 1.1rem;
    font-weight: 700;
}

@media (min-width: 1024px) {
    .nav-menu {
        display: block;
    }

    .nav-actions {
        display: block;
    }
}

/* ===== MOBILE MENU ===== */
.nav-toggle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.hamburger-line {
    width: 100%;
    height: 2px;
    background: var(--secondary-700);
    margin: 3px 0;
    transition: all var(--transition-normal);
    transform-origin: center;
}

.nav-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--gradient-secondary);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: var(--space-xl);
    text-align: center;
}

.mobile-menu-header {
    position: absolute;
    top: var(--space-xl);
    left: var(--space-xl);
    right: var(--space-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-logo {
    height: 60px;
}

.mobile-menu-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--space-sm);
}

.mobile-nav {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.mobile-nav-link {
    color: white;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 600;
    transition: all var(--transition-normal);
}

.mobile-nav-link:hover {
    color: var(--primary-400);
    transform: translateX(10px);
}

@media (min-width: 1024px) {
    .nav-toggle {
        display: none;
    }

    .mobile-menu-overlay {
        display: none;
    }
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: calc(var(--space-4xl) + 120px) 0 var(--space-4xl);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hero);
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    background-size: 100px 100px;
}

.hero .container {
    position: relative;
    z-index: 1;
}

.hero-content {
    text-align: center;
    color: white;
    max-width: 900px;
    margin: 0 auto var(--space-4xl);
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--space-xl);
    animation: fadeInUp 0.8s ease 0.2s both;
}

.hero-title {
    margin-bottom: var(--space-xl);
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 0.8s ease 0.4s both;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: var(--space-2xl);
    opacity: 0.9;
    animation: fadeInUp 0.8s ease 0.6s both;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    align-items: center;
    margin-bottom: var(--space-4xl);
    animation: fadeInUp 0.8s ease 0.8s both;
}

@media (min-width: 640px) {
    .hero-actions {
        flex-direction: row;
        justify-content: center;
    }
}

/* ===== HERO STATS ===== */
.hero-stats {
    margin-bottom: var(--space-2xl);
    animation: fadeInUp 0.8s ease 1s both;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
    max-width: 800px;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-xl);
    }
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    text-align: center;
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-md);
    font-size: 1.5rem;
    color: white;
}

.stat-content .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: var(--space-sm);
    font-family: var(--font-secondary);
}

.stat-content .stat-label {
    font-size: 1rem;
    font-weight: 600;
    color: white;
    margin-bottom: var(--space-xs);
}

.stat-content .stat-description {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

/* ===== SCROLL INDICATOR ===== */
.scroll-indicator {
    text-align: center;
    animation: fadeInUp 0.8s ease 1.2s both;
}

.scroll-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    margin-bottom: var(--space-sm);
}

.scroll-arrow {
    color: white;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

/* ===== FLOATING ELEMENTS ===== */
.hero-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-element {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.floating-1 {
    top: 20%;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.floating-2 {
    top: 60%;
    right: 15%;
    animation: float 8s ease-in-out infinite reverse;
}

.floating-3 {
    bottom: 30%;
    left: 20%;
    animation: float 7s ease-in-out infinite;
}

@media (max-width: 768px) {
    .hero-floating-elements {
        display: none;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* ===== SOBRE SECTION ===== */
.sobre {
    padding: var(--space-4xl) 0;
    background: var(--secondary-50);
    position: relative;
}

.sobre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-200), transparent);
}

.sobre-content {
    position: relative;
}

.sobre-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    margin-bottom: var(--space-4xl);
}

@media (min-width: 768px) {
    .sobre-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.sobre-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--secondary-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.sobre-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.sobre-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.card-header {
    margin-bottom: var(--space-lg);
}

.sobre-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-lg);
    font-size: 1.8rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.sobre-card h3 {
    color: var(--secondary-900);
    margin-bottom: var(--space-md);
    font-size: 1.25rem;
}

.sobre-card p {
    color: var(--secondary-600);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.card-footer {
    margin-top: auto;
}

.card-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--primary-600);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all var(--transition-normal);
}

.card-link:hover {
    color: var(--primary-700);
    gap: var(--space-md);
}

/* ===== SOBRE STATS ===== */
.sobre-stats {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--secondary-200);
}

.stats-header {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.stats-header h3 {
    color: var(--secondary-900);
    margin-bottom: var(--space-sm);
}

.stats-header p {
    color: var(--secondary-600);
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.sobre-stats .stat-item {
    text-align: center;
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    background: var(--secondary-50);
    transition: all var(--transition-normal);
}

.sobre-stats .stat-item:hover {
    background: var(--primary-50);
    transform: translateY(-3px);
}

.sobre-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-600);
    margin-bottom: var(--space-sm);
    font-family: var(--font-secondary);
}

.sobre-stats .stat-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--secondary-700);
}

/* ===== FORM STYLES ===== */
.multi-step-form {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--secondary-200);
    max-width: 600px;
    margin: 0 auto;
}

.form-step {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.form-step.active {
    display: block;
}

.form-step h3 {
    text-align: center;
    color: var(--secondary-900);
    margin-bottom: var(--space-xl);
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-sm);
    font-weight: 600;
    color: var(--secondary-700);
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    border: 2px solid var(--secondary-200);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-family: var(--font-primary);
    transition: all var(--transition-normal);
    background: var(--secondary-50);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    background: white;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.service-options {
    display: grid;
    gap: var(--space-md);
    margin-bottom: var(--space-xl);
}

.service-option {
    cursor: pointer;
}

.service-option input[type="radio"] {
    display: none;
}

.option-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    padding: var(--space-lg);
    border: 2px solid var(--secondary-200);
    border-radius: var(--radius-lg);
    background: var(--secondary-50);
    transition: all var(--transition-normal);
}

.service-option input[type="radio"]:checked + .option-content {
    border-color: var(--primary-500);
    background: var(--primary-50);
}

.option-icon {
    width: 50px;
    height: 50px;
    background: var(--secondary-200);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--secondary-600);
    transition: all var(--transition-normal);
}

.service-option input[type="radio"]:checked + .option-content .option-icon {
    background: var(--primary-500);
    color: white;
}

.option-text h4 {
    margin: 0 0 var(--space-xs) 0;
    color: var(--secondary-900);
    font-size: 1.1rem;
}

.option-text p {
    margin: 0;
    color: var(--secondary-600);
    font-size: 0.9rem;
}

.form-navigation {
    display: flex;
    gap: var(--space-md);
    margin-top: var(--space-xl);
}

.form-navigation .btn {
    flex: 1;
}

.prev-step {
    background: var(--secondary-500);
    color: white;
}

.prev-step:hover {
    background: var(--secondary-600);
}

/* ===== PROGRESS INDICATOR ===== */
.form-progress {
    display: flex;
    justify-content: center;
    gap: var(--space-md);
    margin: var(--space-xl) 0;
}

.progress-step {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background: var(--secondary-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--secondary-500);
    transition: all var(--transition-normal);
    font-family: var(--font-secondary);
}

.progress-step.active {
    background: var(--primary-500);
    color: white;
    transform: scale(1.1);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--secondary-900);
    color: white;
    padding: var(--space-4xl) 0 var(--space-xl);
}

.footer-content {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.footer-info p {
    margin: var(--space-sm) 0;
    color: var(--secondary-300);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.footer-info i {
    color: var(--primary-400);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--space-xl);
    border-top: 1px solid var(--secondary-700);
}

.footer-bottom p {
    margin: var(--space-xs) 0;
    color: var(--secondary-400);
    font-size: 0.9rem;
}

/* ===== WHATSAPP FLOAT ===== */
.whatsapp-float {
    position: fixed;
    bottom: var(--space-xl);
    right: var(--space-xl);
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    transition: all var(--transition-normal);
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    background: #128c7e;
    transform: scale(1.1);
    box-shadow: var(--shadow-2xl);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* ===== UTILITY CLASSES ===== */
.fade-in-up {
    animation: fadeInUp 0.8s ease both;
}

.loaded {
    overflow-x: hidden;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --space-4xl: 3rem;
        --space-3xl: 2.5rem;
        --space-2xl: 2rem;
    }

    .container {
        padding: 0 var(--space-md);
    }

    .hero {
        min-height: 90vh;
        padding: calc(var(--space-2xl) + 90px) 0 var(--space-2xl);
    }

    .navbar {
        padding: var(--space-xl) 0;
    }

    .header.scrolled .navbar {
        padding: var(--space-lg) 0;
    }

    .nav-brand .logo {
        height: 70px;
    }

    .header.scrolled .nav-brand .logo {
        height: 50px;
    }

    .nav-cta {
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
    }

    .hero-content {
        margin-bottom: var(--space-2xl);
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: var(--space-xs) var(--space-md);
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .stats-container {
        gap: var(--space-md);
    }

    .stat-card {
        padding: var(--space-lg);
    }

    .stat-content .stat-number {
        font-size: 2rem;
    }

    .sobre-grid {
        gap: var(--space-lg);
    }

    .sobre-card {
        padding: var(--space-lg);
    }

    .sobre-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .multi-step-form {
        padding: var(--space-lg);
        margin: 0 var(--space-md);
    }

    .option-content {
        gap: var(--space-md);
        padding: var(--space-md);
    }

    .option-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .form-navigation {
        flex-direction: column;
    }

    .whatsapp-float {
        width: 50px;
        height: 50px;
        bottom: var(--space-lg);
        right: var(--space-lg);
        font-size: 1.25rem;
    }
}

/* ===== SERVIÇOS SECTION ===== */
.servicos {
    padding: var(--space-4xl) 0;
    background: white;
    position: relative;
}

.servicos::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-200), transparent);
}

.servicos-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    margin-bottom: var(--space-4xl);
}

@media (min-width: 768px) {
    .servicos-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .servicos-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.servico-card {
    background: var(--secondary-50);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    border: 1px solid var(--secondary-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.servico-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.servico-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    background: white;
}

.servico-header {
    margin-bottom: var(--space-lg);
}

.servico-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-lg);
    font-size: 1.8rem;
    color: white;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.servico-card:hover .servico-icon {
    transform: scale(1.1);
}

.servico-card h3 {
    color: var(--secondary-900);
    margin-bottom: var(--space-md);
    font-size: 1.25rem;
}

.servico-header p {
    color: var(--secondary-600);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.servico-list {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
}

.servico-list li {
    display: flex;
    align-items: flex-start;
    gap: var(--space-sm);
    padding: var(--space-sm) 0;
    color: var(--secondary-700);
    font-size: 0.95rem;
    line-height: 1.5;
}

.servico-list li i {
    color: var(--primary-500);
    font-size: 0.8rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.servicos-cta {
    background: var(--gradient-hero);
    border-radius: var(--radius-2xl);
    padding: var(--space-4xl) var(--space-2xl);
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.servicos-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    background-size: 100px 100px;
}

.cta-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;
}

.servicos-cta h3 {
    font-size: 2rem;
    margin-bottom: var(--space-lg);
    color: white;
}

.servicos-cta p {
    font-size: 1.125rem;
    margin-bottom: var(--space-2xl);
    opacity: 0.9;
    color: white;
}

@media (max-width: 768px) {
    .servicos-cta {
        padding: var(--space-2xl) var(--space-lg);
    }

    .servicos-cta h3 {
        font-size: 1.5rem;
    }

    .servicos-cta p {
        font-size: 1rem;
    }
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #00e676, #00c853);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 230, 118, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #00c853, #00a046);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 230, 118, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #00e676;
    border: 2px solid #00e676;
}

.btn-secondary:hover {
    background: #00e676;
    color: white;
    transform: translateY(-2px);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #00e676;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
}

.hero .container {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto 3rem;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #00e676;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.stat-description {
    font-size: 0.9rem;
    opacity: 0.8;
}

.scroll-indicator {
    margin-top: 2rem;
}

.scroll-indicator a {
    color: white;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.scroll-indicator a:hover {
    opacity: 1;
}

.scroll-indicator i {
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.section-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 0;
}

/* Sobre Section */
.sobre {
    background: #f8f9fa;
}

.sobre-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.sobre-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sobre-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.sobre-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #00e676, #00c853);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.sobre-icon i {
    font-size: 2rem;
    color: white;
}

.sobre-card h3 {
    color: #333;
    margin-bottom: 1rem;
}

.sobre-card p {
    color: #666;
    line-height: 1.6;
}

.sobre-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #00e676;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #00e676;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

/* Serviços Section */
.servicos {
    background: white;
}

.servicos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.servico-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid #00e676;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.servico-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.servico-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00e676, #00c853);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.servico-icon i {
    font-size: 1.5rem;
    color: white;
}

.servico-card h3 {
    color: #333;
    margin-bottom: 1rem;
}

.servico-card > p {
    color: #666;
    margin-bottom: 1.5rem;
}

.servico-list {
    list-style: none;
    padding: 0;
}

.servico-list li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.servico-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #00e676;
    font-weight: bold;
}

.servicos-cta {
    text-align: center;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    color: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-top: 3rem;
}

.servicos-cta h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.servicos-cta p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Contato Section */
.contato {
    background: #f8f9fa;
}

.contato-header {
    text-align: center;
    margin-bottom: 3rem;
}

.contato-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.info-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.info-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00e676, #00c853);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.info-icon i {
    font-size: 1.5rem;
    color: white;
}

.info-item h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.info-item p {
    color: #666;
    margin: 0;
}

/* Multi-Step Form */
.contato-form {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.multi-step-form {
    position: relative;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.form-step h3 {
    text-align: center;
    color: #333;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00e676;
}

.service-options {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.service-option {
    cursor: pointer;
}

.service-option input[type="radio"] {
    display: none;
}

.option-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.service-option input[type="radio"]:checked + .option-content {
    border-color: #00e676;
    background: rgba(0, 230, 118, 0.1);
}

.option-icon {
    width: 50px;
    height: 50px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.service-option input[type="radio"]:checked + .option-content .option-icon {
    background: #00e676;
    color: white;
}

.option-icon i {
    font-size: 1.2rem;
    color: #666;
    transition: color 0.3s ease;
}

.service-option input[type="radio"]:checked + .option-content .option-icon i {
    color: white;
}

.option-text h4 {
    margin: 0 0 0.25rem 0;
    color: #333;
}

.option-text p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.form-navigation {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
}

.form-navigation .btn {
    flex: 1;
}

.prev-step {
    background: #6c757d;
    color: white;
}

.prev-step:hover {
    background: #5a6268;
}

/* Progress Indicator */
.form-progress {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
}

.progress-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
}

.progress-step.active {
    background: #00e676;
    color: white;
}

.form-privacy {
    text-align: center;
    margin-top: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.form-privacy p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    color: #666;
}

.contato-whatsapp {
    text-align: center;
    margin-top: 2rem;
}

.whatsapp-direct {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #25d366;
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.whatsapp-direct:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    text-align: center;
    margin-bottom: 2rem;
}

.footer-info p {
    margin: 0.5rem 0;
    color: #ccc;
}

.footer-info i {
    color: #00e676;
    margin-right: 0.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
}

.footer-bottom p {
    margin: 0.25rem 0;
    color: #999;
    font-size: 0.9rem;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    background: #128c7e;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    /* Header Mobile */
    .nav-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background: white;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: left 0.3s ease;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        gap: 2rem;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-cta {
        display: none;
    }

    /* Hero Mobile */
    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    /* Typography Mobile */
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    /* Sections Mobile */
    section {
        padding: 3rem 0;
    }

    .sobre-grid,
    .servicos-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .sobre-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .contato-info {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .service-options {
        gap: 0.75rem;
    }

    .option-content {
        padding: 0.75rem;
    }

    .form-navigation {
        flex-direction: column;
    }

    .servicos-cta {
        padding: 2rem 1rem;
    }

    .servicos-cta h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .sobre-stats {
        grid-template-columns: 1fr;
    }

    .btn {
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
    }

    .btn-large {
        padding: var(--space-lg) var(--space-xl);
        font-size: 1.1rem;
    }

    .btn-hero {
        padding: var(--space-lg) var(--space-xl);
        font-size: 1.1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Focus States */
.btn:focus,
input:focus,
textarea:focus {
    outline: 2px solid #00e676;
    outline-offset: 2px;
}
