/*
* CG Assessoria - Declaração de Imposto de Renda
* Styles.css
* Mobile-first, responsive design
*/

/* ===== CSS Variables ===== */
:root {
    /* Colors */
    --color-primary: #25D366;
    --color-primary-dark: #000000; /* 10% darker for hover states */
    --color-secondary: #4A6FDC;
    --color-secondary-dark: #3a59b0;
    --color-accent: #FF9500;
    --color-text: #333333;
    --color-text-light: #666666;
    --color-background: #F8F9FA;
    --color-white: #FFFFFF;
    --color-light-gray: #E9ECEF;
    --color-medium-gray: #CED4DA;
    --color-dark-gray: #6C757D;
    --color-success: #28a745;
    --color-warning: #ffc107;
    --color-danger: #dc3545;

    /* Typography */
    --font-family: 'Poppins', sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;
    --border-radius-circle: 50%;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);

    /* Z-index */
    --z-index-dropdown: 100;
    --z-index-sticky: 200;
    --z-index-fixed: 300;
    --z-index-modal: 400;
    --z-index-popover: 500;
    --z-index-tooltip: 600;
}

/* ===== Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

body {
    font-family: var(--font-family);
    color: var(--color-text);
    background-color: var(--color-white);
    line-height: 1.6;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
    position: relative;
}

img {
    max-width: 100%;
    height: auto;
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-dark);
}

ul, ol {
    list-style-position: inside;
}

/* ===== Container ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    overflow: hidden;
    box-sizing: border-box;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--spacing-md);
    line-height: 1.3;
    font-weight: 600;
}

h1 {
    font-size: 2rem; /* 32px */
    margin-bottom: var(--spacing-lg);
}

h2 {
    font-size: 1.75rem; /* 28px */
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

h3 {
    font-size: 1.25rem; /* 20px */
}

h4 {
    font-size: 1.125rem; /* 18px */
    font-weight: 500;
}

p {
    margin-bottom: var(--spacing-md);
}

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-md);
    font-weight: 700;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    font-family: var(--font-family);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    z-index: 1;
    font-size: 1.25rem;
    min-width: 200px;
    min-height: 50px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: width var(--transition-normal);
    z-index: -1;
}

.btn:hover::before {
    width: 100%;
}

.btn:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(-2px);
}

.btn-primary {
    background-color: var(--color-primary);
    color: var(--color-white);
    border: 2px solid var(--color-primary-dark);
    animation: pulse-light 2s infinite;
}

@keyframes pulse-light {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

.btn-primary:hover {
    background-color: var(--color-primary-dark);
    color: var(--color-white);
}

.btn-secondary {
    background-color: var(--color-secondary);
    color: var(--color-white);
    border: 2px solid var(--color-secondary-dark);
}

.btn-secondary:hover {
    background-color: var(--color-secondary-dark);
    color: var(--color-white);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xxl);
    font-size: 1.4rem;
    letter-spacing: 0.5px;
    min-width: 250px;
    min-height: 60px;
}

.btn i {
    font-size: 1.5em;
    margin-right: var(--spacing-sm);
}

/* Focus styles for accessibility */
.btn:focus, a:focus, input:focus, select:focus, textarea:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* ===== Section Spacing ===== */
section {
    padding: var(--spacing-xl) 0;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

/* Removido o header */
body {
    padding-top: 0;
}

/* ===== Hero Section ===== */
.hero {
    text-align: center;
    padding: 0 0 30px 0; /* Adicionado padding inferior para garantir que o azul termine antes do verde */
    min-height: 65vh; /* Altura ainda mais reduzida para desktop */
    display: flex;
    align-items: flex-start; /* Alinhamento no topo em vez de centro */
    padding-top: 0; /* Removido padding superior completamente */
    position: relative;
    background-color: #0a1a41; /* Fundo azul escuro similar à imagem */
    background-image: url('../img/backgrounds/Hero.jpeg');
    background-size: cover;
    background-position: center;
    overflow: visible; /* Alterado para visible para permitir que a próxima seção apareça */
    color: var(--color-white);
    margin-bottom: 0; /* Removida margem negativa para evitar sobreposição */
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    .hero {
        min-height: 85vh; /* Altura reduzida para mobile */
        padding: 20px 0 80px 0; /* Padding ajustado para mobile */
        align-items: center; /* Mantém alinhamento central para mobile */
        margin-bottom: 0; /* Remover margem negativa em mobile */
        overflow: hidden; /* Voltar para hidden em mobile */
    }
}

/* Ajustes específicos para desktop já estão na classe principal */

/* Logo na hero section */
.hero-logo {
    position: relative;
    z-index: 10;
    padding-top: 0; /* Removido padding superior completamente */
    margin-bottom: 0; /* Removido espaço completamente */
    display: flex;
    justify-content: center;
    width: 100%;
    animation: fadeInDown 0.8s ease-out;
}

/* Ajuste específico para desktop */
@media (min-width: 769px) {
    .hero-logo {
        margin-top: 10px; /* Reduzido para subir o logo na versão desktop */
        margin-bottom: 5px; /* Reduzido ainda mais para desktop */
    }
}

.hero-logo img {
    max-width: 420px; /* Tamanho reduzido para desktop */
    height: auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
    transition: all 0.4s ease;
    object-fit: contain;
    image-rendering: -webkit-optimize-contrast; /* Melhora a nitidez em navegadores WebKit */
    image-rendering: crisp-edges; /* Melhora a nitidez em navegadores modernos */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-font-smoothing: subpixel-antialiased;
    will-change: transform, filter;
    transform-origin: center center;
}

.hero-logo img:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.6));
}

@media (max-width: 768px) {
    .hero-logo {
        padding-top: 20px;
        margin-bottom: 15px;
    }

    .hero-logo img {
        max-width: 280px; /* Ligeiramente aumentado para mobile */
    }
}

@media (min-width: 769px) and (max-width: 1023px) {
    .hero-logo img {
        max-width: 380px; /* Tamanho reduzido para tablet */
    }
}

@media (min-width: 1024px) and (max-width: 1439px) {
    .hero-logo img {
        max-width: 400px; /* Tamanho reduzido para desktop médio */
    }
}

@media (min-width: 1440px) {
    .hero-logo img {
        max-width: 450px; /* Tamanho reduzido para desktop grande */
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(10, 26, 65, 0.85) 0%, rgba(10, 26, 65, 0.95) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-xs) var(--spacing-md); /* Padding extremamente reduzido para desktop */
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    margin-top: -20px; /* Valor negativo para subir o conteúdo */
    background-color: rgba(10, 26, 65, 0.7); /* Fundo azul escuro mais transparente */
    backdrop-filter: blur(5px);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Ajuste específico para desktop */
@media (min-width: 769px) {
    .hero-content {
        margin-top: -30px; /* Valor mais negativo para subir ainda mais o conteúdo na versão desktop */
        padding-top: 15px; /* Padding superior reduzido para desktop */
    }
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    .hero-content {
        padding: var(--spacing-lg) var(--spacing-md); /* Padding ajustado para mobile */
        margin-top: 20px; /* Espaço reduzido para aproximar da logo */
    }
}

.hero h1 {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    color: var(--color-white);
    font-weight: 800;
    margin-bottom: 20px; /* Aumentado para acomodar a barra verde */
    animation: fadeInUp 1s ease-out;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    font-size: 2.8rem;
    line-height: 1.2;
    letter-spacing: -0.5px;
    position: relative;
    display: inline-block;
    text-transform: uppercase; /* Texto em maiúsculas como na imagem */
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.2rem; /* Fonte um pouco menor para mobile */
        margin-bottom: var(--spacing-xl); /* Mais espaço abaixo do título em mobile */
    }
}

.hero h1::after {
    content: '';
    position: absolute;
    bottom: -10px; /* Ajustado para ficar mais próximo do texto */
    left: 50%;
    transform: translateX(-50%);
    width: 80px; /* Aumentado para maior visibilidade */
    height: 4px; /* Aumentado para maior visibilidade */
    background-color: var(--color-primary);
    border-radius: 3px;
}

/* Ajuste específico para desktop */
@media (min-width: 769px) {
    .hero h1::after {
        bottom: -3px; /* Posicionamento ainda mais alto para desktop */
        width: 120px; /* Largura maior para desktop */
        height: 5px; /* Altura maior para desktop */
    }

    .hero h1 {
        margin-top: -5px; /* Valor negativo para subir o título */
        margin-bottom: 15px; /* Reduzido para aproximar do subtítulo */
    }
}

.hero h4 {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: var(--spacing-xs); /* Espaçamento reduzido para desktop */
    margin-top: 0; /* Removido espaçamento superior completamente */
    color: rgba(255, 255, 255, 0.9);
    animation: fadeInUp 1s ease-out 0.3s;
    animation-fill-mode: both;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    font-weight: 400;
    font-size: 1.25rem; /* Fonte ligeiramente maior */
    line-height: 1.5;
    padding: 0 var(--spacing-md); /* Adicionando padding horizontal */
    text-transform: uppercase; /* Texto em maiúsculas como na imagem */
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    .hero h4 {
        margin-bottom: var(--spacing-xl); /* Espaçamento aumentado para mobile */
        margin-top: var(--spacing-lg); /* Espaçamento aumentado para mobile */
        font-size: 1.15rem; /* Fonte ajustada para mobile */
        line-height: 1.6; /* Melhorando o espaçamento entre linhas */
        padding: 0 var(--spacing-sm); /* Padding horizontal menor para mobile */
    }
}

.hero-description {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: var(--spacing-sm); /* Espaçamento reduzido */
    color: rgba(255, 255, 255, 0.9);
    animation: fadeInUp 1s ease-out 0.4s;
    animation-fill-mode: both;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    font-weight: 400;
    font-size: 1.2rem;
    line-height: 1.4; /* Linha mais compacta */
    padding: 0 var(--spacing-md);
}

@media (max-width: 768px) {
    .hero-description {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: var(--spacing-lg);
    }
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs); /* Espaçamento extremamente reduzido entre botões */
    align-items: center;
    animation: fadeInUp 1s ease-out 0.6s;
    animation-fill-mode: both;
    margin-top: var(--spacing-sm); /* Espaçamento superior extremamente reduzido */
}

.hero-whatsapp-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.verified-text {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5px;
    font-style: italic;
    text-align: center;
}

/* Ajuste para desktop */
@media (min-width: 769px) {
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
        gap: var(--spacing-xl);
        margin-top: var(--spacing-md);
        align-items: flex-start;
    }

    .hero-whatsapp-container {
        margin-right: var(--spacing-md);
    }

    .hero-buttons .btn-secondary {
        margin-top: 0;
        height: 52px;
        align-self: flex-start;
        margin-top: 0;
    }
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-md); /* Espaçamento reduzido entre botões para mobile */
        margin-top: var(--spacing-md); /* Espaçamento superior reduzido para mobile */
    }
}

.btn-whatsapp {
    transform: scale(1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    background-color: #00e676; /* Verde mais vibrante como na imagem */
    border: none;
    font-size: 1.2rem;
    padding: 12px 25px;
    font-weight: 700;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border-radius: 50px;
    text-transform: uppercase; /* Texto em maiúsculas como na imagem */
    text-align: center;
    line-height: 1.3;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    will-change: transform, box-shadow;
    width: auto;
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    .btn-whatsapp {
        font-size: 1.1rem;
        padding: 10px 20px;
        width: auto; /* Largura automática baseada no conteúdo */
        max-width: 280px;
    }
}

.btn-whatsapp i {
    font-size: 2rem;
    margin-right: 12px;
    margin-left: -3px;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    text-rendering: optimizeLegibility;
    transform: scale(1); /* Tamanho normal para melhor proporção */
    will-change: transform;
    image-rendering: -webkit-optimize-contrast; /* Melhora a nitidez em navegadores WebKit */
    image-rendering: crisp-edges; /* Melhora a nitidez em navegadores modernos */
}

.btn-whatsapp:hover {
    background-color: #00c853; /* Verde mais escuro ao passar o mouse */
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    transform: translateY(-3px) scale(1.02);
}

.hero-buttons .btn-secondary {
    background-color: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--color-white);
    font-weight: 500;
    padding: 12px 24px;
    margin-top: var(--spacing-md); /* Será sobrescrito na versão desktop */
    transition: all 0.3s ease;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-buttons .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Scroll Down Arrow */
.scroll-down-arrow {
    position: absolute;
    bottom: 10px; /* Posição extremamente ajustada para desktop - o mais acima possível */
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    animation: bounce 2s infinite;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

/* Ajuste para dispositivos móveis - posicionamento ajustado */
@media (max-width: 768px) {
    .scroll-down-arrow {
        bottom: auto; /* Removendo o posicionamento bottom fixo */
        position: absolute;
        top: calc(50% + 280px); /* Posição ajustada para o novo layout */
        left: 50%;
        transform: translateX(-50%);
    }
}

.scroll-down-arrow a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px; /* Tamanho padrão para desktop */
    height: 60px; /* Tamanho padrão para desktop */
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    color: var(--color-white);
    font-size: 2.2rem; /* Tamanho da fonte padrão para desktop */
    transition: all 0.3s ease;
    box-shadow: 0 0 25px rgba(255, 255, 255, 0.4), 0 0 15px rgba(37, 211, 102, 0.6);
    border: 3px solid rgba(255, 255, 255, 0.7);
    animation: pulse 2s infinite;
    position: relative;
    overflow: visible;
}

/* Ajuste para dispositivos móveis */
@media (max-width: 768px) {
    .scroll-down-arrow a {
        width: 65px; /* Tamanho aumentado para mobile */
        height: 65px; /* Tamanho aumentado para mobile */
        font-size: 2.5rem; /* Fonte maior para mobile */
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.5), 0 0 20px rgba(37, 211, 102, 0.7); /* Sombra mais intensa para mobile */
    }
}

/* Add a glow effect */
.scroll-down-arrow a::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 70%);
    z-index: -1;
    animation: glow 2s infinite alternate;
}

@keyframes glow {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.2);
    }
}

.scroll-down-arrow a:hover {
    background-color: var(--color-primary);
    color: var(--color-white);
    transform: scale(1.1);
    border-color: var(--color-primary-dark);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(-50%);
    }
    40% {
        transform: translateY(-20px) translateX(-50%);
    }
    60% {
        transform: translateY(-10px) translateX(-50%);
    }
}

/* Animação de bounce específica para mobile */
@media (max-width: 768px) {
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-20px);
        }
        60% {
            transform: translateY(-10px);
        }
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(0, 0, 0, 0.7);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }
}

/* Animation for attention */
@keyframes attention {
    0% {
        transform: scale(1) translateY(0) translateX(-50%);
    }
    10% {
        transform: scale(1.2) translateY(-10px) translateX(-40%);
    }
    20% {
        transform: scale(1) translateY(0) translateX(-50%);
    }
    30% {
        transform: scale(1.2) translateY(-10px) translateX(-40%);
    }
    40% {
        transform: scale(1) translateY(0) translateX(-50%);
    }
    100% {
        transform: scale(1) translateY(0) translateX(-50%);
    }
}

/* Animação de atenção específica para mobile */
@media (max-width: 768px) {
    @keyframes attention {
        0% {
            transform: scale(1);
        }
        10% {
            transform: scale(1.2) translateY(-10px);
        }
        20% {
            transform: scale(1);
        }
        30% {
            transform: scale(1.2) translateY(-10px);
        }
        40% {
            transform: scale(1);
        }
        100% {
            transform: scale(1);
        }
    }
}

.scroll-down-arrow.attention {
    animation: attention 2s ease-in-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Proof Section ===== */
.proof {
    background-color: #00E676; /* Verde vibrante como na imagem */
    padding: 2.5rem 0;
    color: var(--color-text);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1; /* Garantir que fique acima da hero section */
}

.proof-banner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0;
    max-width: 100%;
    margin: 0 auto;
    position: relative;
    text-align: center;
}

/* Ajuste específico para desktop */
@media (min-width: 769px) {
    .proof {
        padding: 2rem 0; /* Padding reduzido para desktop */
    }
}

.proof-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
}

.proof-title {
    font-size: 2.8rem;
    font-weight: 900;
    margin-bottom: 0.3rem;
    color: #000;
    text-transform: uppercase;
    letter-spacing: 0px;
    line-height: 1.1;
    text-align: center;
    font-family: var(--font-family);
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5); /* Sombra sutil para destacar o texto */
}

.proof-subtitle {
    font-size: 2.3rem;
    font-weight: 800;
    margin: 0;
    color: #000;
    text-transform: uppercase;
    letter-spacing: 0px;
    line-height: 1.1;
    text-align: center;
    font-family: var(--font-family);
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5); /* Sombra sutil para destacar o texto */
}

/* Ajuste específico para desktop */
@media (min-width: 769px) {
    .proof-title {
        font-size: 3rem; /* Tamanho maior para desktop */
    }

    .proof-subtitle {
        font-size: 2.5rem; /* Tamanho maior para desktop */
    }
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    .proof-title {
        font-size: 2rem;
        line-height: 1.1;
    }

    .proof-subtitle {
        font-size: 1.7rem;
        line-height: 1.1;
    }

    .proof {
        padding: 1.5rem 0;
    }

    .proof-banner {
        padding: 0.3rem 0;
    }
}

.highlight {
    color: var(--color-white);
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.5);
}

/* ===== Pains Section ===== */
.pains {
    background-color: var(--color-white);
    margin-top: 1rem;
}

.pains h2 {
    margin-bottom: var(--spacing-xl);
}

.pains-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.pains-list li {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.pains-list li:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.icon-alert {
    flex-shrink: 0;
    font-size: 1.5rem;
    color: var(--color-warning);
    background-color: rgba(255, 193, 7, 0.1);
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-circle);
}

.pain-content {
    flex: 1;
}

.pain-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--color-text);
}

.pain-content p {
    color: var(--color-text-light);
    margin-bottom: 0;
}

/* ===== Benefits Section ===== */
.benefits {
    background-color: var(--color-background);
    background-image: linear-gradient(to bottom, var(--color-background), white);
}

.benefits h2 {
    margin-bottom: var(--spacing-xl);
}

.benefits-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

.benefit-card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--color-light-gray);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(to bottom, rgba(37, 211, 102, 0.05), transparent);
    transition: height var(--transition-normal);
    z-index: -1;
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.benefit-card:hover::before {
    height: 100%;
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background-color: var(--color-primary);
    color: var(--color-white);
    border-radius: var(--border-radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: 2rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.benefit-card:hover .benefit-icon {
    transform: scale(1.1);
}

.benefit-card h3 {
    margin-bottom: var(--spacing-md);
    font-size: 1.25rem;
    color: var(--color-text);
    font-weight: 600;
}

.benefit-card p {
    color: var(--color-text-light);
    margin-bottom: 0;
    font-size: 0.95rem;
    line-height: 1.6;
}

/* ===== CTA-1 Section ===== */
.cta-1 {
    text-align: center;
    margin-top: var(--spacing-xl);
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
    color: var(--color-white);
    padding: var(--spacing-xxl) 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

.cta-1::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    width: 150px;
    height: 150px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-circle);
    animation: float 8s infinite ease-in-out;
}

.cta-1::after {
    content: '';
    position: absolute;
    bottom: -70px;
    right: -70px;
    width: 200px;
    height: 200px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-circle);
    animation: float 10s infinite ease-in-out reverse;
}

@keyframes float {
    0% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(30px, 20px);
    }
    100% {
        transform: translate(0, 0);
    }
}

.cta-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-lg) 0;
}

.cta-1 h2 {
    color: var(--color-white);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
    font-size: 2.2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cta-1 p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-xl);
    font-size: 1.25rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.cta-whatsapp-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
}

.cta-1 .btn {
    transform: scale(1);
    margin-top: var(--spacing-md);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    font-size: 1.2rem;
    padding: 12px 25px;
    font-weight: 700;
    width: fit-content;
    max-width: 280px;
    display: inline-flex;
}

.cta-1 .verified-text {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5px;
    font-style: italic;
    text-align: center;
}

.cta-1 .btn i {
    font-size: 2rem;
    margin-right: 12px;
    margin-left: -3px;
}

/* ===== Testimonials Section ===== */
.testimonials {
    background-color: var(--color-white);
    padding-bottom: var(--spacing-xxl);
}

.testimonials-header {
    margin-bottom: var(--spacing-xl);
}

.testimonials-title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.testimonials-title-content {
    flex: 1;
    max-width: 600px;
}

.testimonials-title-content h2 {
    margin-bottom: var(--spacing-sm);
    text-align: left;
}

.testimonials .section-description {
    text-align: left;
    max-width: 100%;
    margin: 0;
    color: var(--color-text-light);
    font-size: 1.1rem;
}

.testimonials-google-logo-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 200px;
}

.testimonials-header-logo {
    height: 80px;
    width: auto;
    object-fit: contain;
    max-width: 200px;
}

@media (max-width: 767px) {
    .testimonials-title-container {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .testimonials-title-content h2 {
        text-align: center;
    }

    .testimonials .section-description {
        text-align: center;
        margin-bottom: var(--spacing-sm);
        font-size: 1.15rem;
    }

    .testimonials-google-logo-container {
        justify-content: center;
        min-width: auto;
        width: 100%;
        margin-top: -20px;
    }

    .testimonials-header-logo {
        height: 90px;
        max-width: 240px;
    }
}

@media (max-width: 480px) {
    .testimonials-header-logo {
        height: 80px;
        max-width: 220px;
    }
}

.testimonials-carousel {
    position: relative;
    margin-top: var(--spacing-xl);
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    padding: 0 var(--spacing-md);
}

.swiper-container {
    width: 100%;
    padding-bottom: var(--spacing-xl);
    overflow: visible; /* Permite que as sombras dos cards sejam visíveis */
}

.swiper-wrapper {
    display: flex;
    transition: transform 0.3s ease-in-out;
    max-width: 100%;
    gap: var(--spacing-md); /* Espaçamento entre slides */
}

.swiper-slide {
    flex-shrink: 0;
    max-width: 100%;
    height: auto; /* Altura automática */
    display: flex;
    justify-content: center;
}

/* Ajustes responsivos para o carrossel */
@media (min-width: 768px) {
    .testimonials-carousel {
        padding: 0 var(--spacing-lg);
    }

    .swiper-wrapper {
        gap: var(--spacing-lg);
    }
}

@media (min-width: 992px) {
    .testimonials-carousel {
        padding: 0 var(--spacing-xl);
    }

    .swiper-wrapper {
        gap: var(--spacing-xl);
    }

    .testimonials-header-logo {
        height: 100px;
        max-width: 240px;
    }

    .testimonials-title-container {
        gap: var(--spacing-xxl);
    }
}

@media (min-width: 1200px) {
    .testimonials-carousel {
        padding: 0 var(--spacing-xxl);
    }

    .testimonials-header-logo {
        height: 120px;
        max-width: 280px;
    }

    .testimonials-title-content {
        max-width: 700px;
    }
}

.testimonial-card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    width: 100%;
    max-width: 350px; /* Limitando a largura máxima para desktop */
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.testimonial-img-container {
    width: 100%;
    height: auto;
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.testimonial-img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover; /* Voltando para cover mas com ajustes de altura */
    transition: transform 0.5s ease;
    max-height: 500px; /* Altura máxima para desktop */
}

.testimonial-card:hover .testimonial-img {
    transform: scale(1.05);
}

/* Mantendo os estilos antigos para compatibilidade */
.avatar-icon {
    display: none; /* Escondendo os ícones genéricos */
}

.testimonial-content {
    padding: var(--spacing-sm);
    background-color: var(--color-white);
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 2px solid var(--color-light-gray);
}

.testimonial-name {
    font-weight: 600;
    margin-bottom: 0;
    color: var(--color-primary);
    font-size: 0.95rem;
    text-align: center;
    padding: var(--spacing-xs) 0;
}

/* Ajustes responsivos */
@media (min-width: 768px) {
    .testimonial-name {
        font-size: 1rem;
        padding: var(--spacing-sm) 0;
    }

    .testimonial-content {
        padding: var(--spacing-md);
    }
}

/* Ajustes específicos para mobile */
@media (max-width: 767px) {
    .testimonial-card {
        max-width: 280px;
        margin: 0 auto;
    }

    .testimonial-img-container {
        max-height: 280px;
    }

    .testimonial-img {
        max-height: 280px;
    }

    .swiper-wrapper {
        gap: var(--spacing-sm); /* Espaçamento menor entre slides no mobile */
    }
}

.swiper-button-prev,
.swiper-button-next {
    color: var(--color-primary);
}

.swiper-pagination-bullet-active {
    background-color: var(--color-primary);
}

/* ===== Steps Section ===== */
.steps {
    background-color: var(--color-background);
    padding: var(--spacing-xxl) 0;
}

.steps h2 {
    margin-bottom: var(--spacing-xl);
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    position: relative;
}

.steps-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    height: 100%;
    width: 2px;
    background-color: var(--color-light-gray);
    transform: translateX(-50%);
    z-index: 0;
    display: none;
}

.step {
    text-align: center;
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    position: relative;
    z-index: 1;
    transition: all var(--transition-normal);
}

.step:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.step-number {
    margin-bottom: var(--spacing-md);
}

.step-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--color-primary);
    color: var(--color-white);
    border-radius: var(--border-radius-circle);
    font-weight: 700;
    font-size: 1.25rem;
    box-shadow: var(--shadow-sm);
}

.step-icon {
    font-size: 2.5rem;
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
}

.step h3 {
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
    font-weight: 600;
}

.step p {
    color: var(--color-text-light);
    margin-bottom: 0;
}

/* ===== Slogan Section ===== */
.slogan {
    background-color: var(--color-secondary);
    text-align: center;
    padding: var(--spacing-xxl) 0;
    color: var(--color-white);
    position: relative;
    overflow: hidden;
}

.slogan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(37, 211, 102, 0.2), rgba(74, 111, 220, 0.2));
    z-index: 1;
}

.slogan-content {
    position: relative;
    z-index: 2;
}

.slogan h3 {
    letter-spacing: 1px;
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slogan p {
    font-size: 1.125rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    color: rgba(255, 255, 255, 0.9);
}

/* ===== FAQ Section ===== */
.faq {
    background-color: var(--color-background);
    padding: var(--spacing-xxl) 0;
}

.faq h2 {
    margin-bottom: var(--spacing-xl);
}

.faq-accordion {
    margin-top: var(--spacing-xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.faq-item {
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    border: 1px solid var(--color-light-gray);
}

.faq-item:hover {
    box-shadow: var(--shadow-md);
}

.faq-item.active {
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--color-primary);
}

.faq-question {
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.faq-question h3 {
    margin-bottom: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text);
    transition: color var(--transition-fast);
}

.faq-item.active .faq-question h3 {
    color: var(--color-primary);
}

.faq-icon {
    color: var(--color-dark-gray);
    transition: all var(--transition-fast);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.faq-item.active .faq-icon {
    transform: rotate(180deg);
    color: var(--color-primary);
}

.faq-answer {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    display: none;
    color: var(--color-text-light);
    border-top: 1px solid var(--color-light-gray);
}

.faq-item.active .faq-answer {
    display: block;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ===== Form Section ===== */
.form {
    background-color: var(--color-white);
    padding: var(--spacing-xxl) 0;
    background-image: linear-gradient(to bottom, var(--color-white), var(--color-background));
}

.form h2 {
    margin-bottom: var(--spacing-xl);
}

.form form {
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--color-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-light-gray);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--color-text);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--color-medium-gray);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family);
    font-size: 1rem;
    transition: all var(--transition-fast);
    background-color: var(--color-background);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
    outline: none;
    background-color: var(--color-white);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--color-medium-gray);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form button {
    width: 100%;
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    font-weight: 700;
    letter-spacing: 0.5px;
    font-size: 1.2rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    background-color: #00e676; /* Verde mais vibrante como na imagem */
    border: none;
    border-radius: 50px;
    text-transform: uppercase;
    line-height: 1.3;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    will-change: transform, box-shadow;
    animation: pulse-light 2s infinite;
}

.form button i {
    font-size: 2rem;
    margin-right: 12px;
    margin-left: -3px;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    text-rendering: optimizeLegibility;
    transform: scale(1);
    will-change: transform;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

.form button:hover {
    transform: translateY(-3px) scale(1.02);
    background-color: #00c853; /* Verde mais escuro ao passar o mouse */
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

/* ===== Footer Section ===== */
.footer {
    background-color: var(--color-text);
    color: var(--color-white);
    padding: var(--spacing-lg) 0;
    position: relative;
    font-size: 0.9rem;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--color-primary), var(--color-secondary));
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

/* Footer logo removed */

.footer-info p {
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
}

.footer-info p i {
    color: var(--color-primary);
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

/* Centralizar dados de contato na versão desktop */
@media (min-width: 1024px) {
    .footer-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        width: 100%;
    }

    .footer-info p {
        justify-content: center;
        font-size: 1rem;
        margin-bottom: var(--spacing-sm);
    }

    .footer-info p i {
        font-size: 1.2rem;
        width: 20px;
    }
}

/* Redes sociais removidas */

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (min-width: 1024px) {
    .footer-bottom {
        padding-top: var(--spacing-lg);
        margin-top: var(--spacing-lg);
    }
}

.footer-bottom p {
    margin-bottom: var(--spacing-xs);
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
}

/* ===== WhatsApp Float Button ===== */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 100px;
    height: 100px;
    background-color: #00e676; /* Verde mais vibrante como na imagem */
    color: var(--color-white);
    border-radius: var(--border-radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4.5rem;
    box-shadow: var(--shadow-lg);
    z-index: var(--z-index-fixed);
    transition: all var(--transition-normal);
    border: 3px solid rgba(255, 255, 255, 0.7);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    will-change: transform, box-shadow;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.whatsapp-float::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #00e676; /* Verde mais vibrante como na imagem */
    border-radius: var(--border-radius-circle);
    animation: pulse 2s infinite;
    z-index: -1;
}

.whatsapp-float::after {
    content: 'Fale com contador';
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--color-text);
    color: var(--color-white);
    padding: 5px 10px;
    border-radius: var(--border-radius-md);
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
}

.whatsapp-float:hover::after {
    opacity: 1;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    70% {
        transform: scale(1.5);
        opacity: 0;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

.whatsapp-float i {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15));
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 4.5rem; /* Tamanho explícito para melhor renderização */
    transform: scale(1.01); /* Ligeiramente maior para melhor definição */
    will-change: transform;
    image-rendering: -webkit-optimize-contrast; /* Melhora a nitidez em navegadores WebKit */
    image-rendering: crisp-edges; /* Melhora a nitidez em navegadores modernos */
}

.whatsapp-float:hover {
    transform: scale(1.15);
    background-color: #00c853; /* Verde mais escuro ao passar o mouse */
    box-shadow: 0 0 20px rgba(37, 211, 102, 0.6);
}

/* Scroll to Top Button removed */

/* ===== SVG Icon Improvements ===== */
.svg-inline--fa {
    display: inline-block;
    height: 1em;
    overflow: visible;
    vertical-align: -0.125em;
    transform-origin: center;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    backface-visibility: hidden;
}

.fa-whatsapp.svg-inline--fa {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15));
    transform: scale(1.01); /* Ligeiramente maior para melhor definição */
    will-change: transform;
}

/* ===== Animation Effects ===== */
.fade-in-section {
    opacity: 0;
    transform: translateY(40px);
    visibility: hidden;
    transition: opacity 0.8s ease-out, transform 0.8s cubic-bezier(0.17, 0.67, 0.35, 1.02);
    will-change: opacity, visibility, transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

.fade-in-section.is-visible {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

/* Hero content special animation */
.hero-content.fade-in-section {
    transform: scale(0.95) translateY(20px);
}

.hero-content.fade-in-section.is-visible {
    transform: scale(1) translateY(0);
}

/* Card hover effects with animation */
.benefit-card.is-visible:hover,
.step.is-visible:hover,
.faq-item.is-visible:hover {
    transform: translateY(-10px) scale(1.02);
    transition: transform 0.3s ease-out;
}

/* WhatsApp float special animation */
.whatsapp-float.fade-in-section {
    transform: scale(0.8);
}

.whatsapp-float.fade-in-section.is-visible {
    transform: scale(1);
    animation: bounce 1s ease-out 0.5s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
    40% {transform: translateY(-20px);}
    60% {transform: translateY(-10px);}
}

/* Delay for staggered animations */
.delay-1 {
    transition-delay: 0.1s;
}
.delay-2 {
    transition-delay: 0.2s;
}
.delay-3 {
    transition-delay: 0.3s;
}
.delay-4 {
    transition-delay: 0.4s;
}
.delay-5 {
    transition-delay: 0.5s;
}

/* ===== Responsive Styles ===== */

/* Mobile adjustments for larger buttons */
@media (max-width: 767px) {
    .btn {
        min-width: 85%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
    }

    .btn-large {
        min-width: 90%;
    }

    .form button {
        font-size: 1.1rem;
        padding: 10px 20px;
    }

    .form button i {
        font-size: 1.8rem;
    }

    .whatsapp-float {
        width: 90px;
        height: 90px;
        font-size: 3.8rem;
        right: 20px;
        bottom: 20px;
    }

    /* Scroll to top button removed */

    .cta-1 h2 {
        font-size: 1.8rem;
    }

    .cta-1 p {
        font-size: 1.1rem;
    }

    .hero-buttons .btn {
        margin-bottom: var(--spacing-sm);
    }

    /* Redes sociais removidas */

    .logo-image {
        height: 40px;
    }

    /* Footer logo removed */

    .header {
        padding: var(--spacing-sm) 0;
    }

    .hero {
        min-height: 100vh;
        padding: 0;
    }

    .hero-content {
        padding: var(--spacing-xl) var(--spacing-md);
        margin: 0 var(--spacing-md);
        width: auto;
    }

    .hero h1 {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero h1::after {
        width: 50px;
        height: 2px;
        bottom: -10px;
    }

    .hero h4 {
        font-size: 1rem;
        line-height: 1.5;
        margin-top: var(--spacing-md);
    }

    .hero-buttons .btn-primary {
        font-size: 1.2rem;
        padding: 14px 26px;
        width: 100%;
        max-width: 300px;
    }

    .scroll-down-arrow {
        bottom: 5px;
    }

    .scroll-down-arrow a {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .hero-buttons .btn-primary i {
        font-size: 2.5rem;
    }

    .hero-buttons .btn-secondary {
        padding: 10px 20px;
        width: 100%;
        max-width: 300px;
    }
}

/* Tablet (768px and up) */
@media (min-width: 768px) {
    h1 {
        font-size: 2.5rem; /* 40px */
    }

    h2 {
        font-size: 2rem; /* 32px */
    }

    .nav-menu {
        display: block;
        position: static;
        transform: none;
        opacity: 1;
        visibility: visible;
        box-shadow: none;
        padding: 0;
        width: auto;
    }

    .nav-list {
        flex-direction: row;
        padding: 0;
    }

    .mobile-menu-toggle {
        display: none;
    }

    .hero-buttons {
        flex-direction: row;
        justify-content: center;
        gap: var(--spacing-lg);
    }

    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .pains-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
    }

    .steps-container::before {
        display: block;
    }

    .footer-content {
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .footer-info {
        width: 100%;
        text-align: center;
    }

    /* Redes sociais removidas */

    .btn {
        min-width: 220px;
    }

    .btn-large {
        min-width: 280px;
    }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
    h1 {
        font-size: 3rem; /* 48px */
    }

    .container {
        padding: 0 var(--spacing-xl);
    }

    .hero {
        padding: var(--spacing-xxl) 0;
    }

    .hero-content {
        padding: var(--spacing-xxl) 0;
    }

    .benefits-grid {
        grid-template-columns: repeat(5, 1fr);
    }

    .pains-list {
        grid-template-columns: repeat(3, 1fr);
    }

    .steps-container {
        flex-direction: row;
        justify-content: space-between;
        gap: var(--spacing-xl);
    }

    .step {
        flex: 1;
    }

    .testimonial-card {
        padding: var(--spacing-xl);
    }
}

/* Large Desktop (1440px and up) */
@media (min-width: 1440px) {
    .container {
        max-width: 1320px;
    }

    h1 {
        font-size: 3.5rem; /* 56px */
    }

    .hero-content {
        padding: var(--spacing-xxl) 0;
    }

    .benefit-card {
        padding: var(--spacing-xxl);
    }
}

/* Retina Display */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn-whatsapp i, .whatsapp-float i, .fa-whatsapp.svg-inline--fa {
        transform: scale(1.02); /* Ligeiramente maior para telas de alta resolução */
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }
}
